# Requirements extracted from install.sh
# Core scientific computing packages
numpy==1.21.5
pandas==1.4.2
scipy==1.7.3

# Image processing and computer vision
Pillow==9.0.1
opencv-python==********
scikit-image==0.19.2
imageio==2.9.0
imgaug==0.4.0
imutils==0.5.4
albumentations==1.1.0
kornia

# Machine learning and deep learning
scikit-learn==1.0.2
torch==1.12.0+cu113
torchvision==0.13.0+cu113
torchaudio==0.12.0
efficientnet-pytorch==0.7.1
timm==0.6.12
segmentation-models-pytorch==0.3.2
torchtoolbox==*******
transformers
loralib
einops

# Face detection and tracking
dlib==19.24.0
filterpy

# Visualization and monitoring
seaborn==0.11.2
tensorboard==2.10.1

# Utilities
tqdm==4.61.0
pyyaml==6.0
simplejson
setuptools==59.5.0
fvcore

# Git repositories
git+https://github.com/openai/CLIP.git

# Note: PyTorch packages (torch, torchvision, torchaudio) require the following extra index URL:
# --extra-index-url https://download.pytorch.org/whl/cu113
